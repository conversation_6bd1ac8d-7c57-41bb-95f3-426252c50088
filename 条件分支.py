# 条件分支示例：演示如何创建一个基于条件判断的多智能体工作流
# 功能：创建三个智能体，根据输入语言类型自动选择不同的处理分支

import asyncio

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.conditions import MaxMessageTermination
from autogen_agentchat.teams import DiGraphBuilder, GraphFlow
from autogen_ext.models.openai import OpenAIChatCompletionClient
from llm import get_llm

async def main():
    # 使用 OpenAI 模型客户端初始化智能体
    api_key = "1932375702800756788"
    model_client = get_llm("deepseek-v3-friday", api_key)
    agent_a = AssistantAgent(
        "A",
        model_client=model_client,
        system_message="检测输入是否为中文。如果是中文，请说'是'，否则说'否'，不要说其他内容。",
    )
    agent_b = AssistantAgent("B", model_client=model_client, system_message="将输入内容翻译成英文。")
    agent_c = AssistantAgent("C", model_client=model_client, system_message="将输入内容翻译成中文。")

    # 创建条件分支流程的有向图 A -> B（"是"），A -> C（其他情况）
    builder = DiGraphBuilder()
    builder.add_node(agent_a).add_node(agent_b).add_node(agent_c)
    # 创建检查消息内容的条件函数
    builder.add_edge(agent_a, agent_b, condition=lambda msg: "是" in msg.to_model_text())
    builder.add_edge(agent_a, agent_c, condition=lambda msg: "是" not in msg.to_model_text())
    graph = builder.build()

    # 使用有向图创建 GraphFlow 团队
    team = GraphFlow(
        participants=[agent_a, agent_b, agent_c],
        graph=graph,
        termination_condition=MaxMessageTermination(5),
    )

    # 运行团队并打印事件
    async for event in team.run_stream(task="AutoGen是一个用于构建AI智能体的框架。"):
        print(event)
        print()


asyncio.run(main())
