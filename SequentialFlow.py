import asyncio

from autogen_agentchat.agents import Assistant<PERSON>gent
from autogen_agentchat.conditions import MaxMessageTermination
from autogen_agentchat.teams import <PERSON><PERSON>raph<PERSON><PERSON>er, GraphFlow
from autogen_ext.models.openai import OpenAIChatCompletionClient
from llm import get_llm

async def main():
    # Initialize agents with OpenAI model clients.
    api_key = "1932375702800756788"
    model_client = get_llm("deepseek-v3-friday", api_key)
    agent_a = AssistantAgent("A", model_client=model_client, system_message="You are a helpful assistant.")
    agent_b = AssistantAgent("B", model_client=model_client, system_message="Translate input to Chinese.")
    agent_c = AssistantAgent("C", model_client=model_client, system_message="Translate input to English.")

    # Create a directed graph with sequential flow A -> B -> C.
    builder = DiGraphBuilder()
    builder.add_node(agent_a).add_node(agent_b).add_node(agent_c)
    builder.add_edge(agent_a, agent_b).add_edge(agent_b, agent_c)
    graph = builder.build()

    # Create a GraphFlow team with the directed graph.
    team = GraphFlow(
        participants=[agent_a, agent_b, agent_c],
        graph=graph,
        termination_condition=MaxMessageTermination(5),
    )

    # Run the team and print the events.
    async for event in team.run_stream(task="Write a short story about a cat."):
        print(event)
        print()


asyncio.run(main())
