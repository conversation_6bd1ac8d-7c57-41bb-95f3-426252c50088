# 循环控制示例：演示如何创建一个带有条件循环的多智能体工作流
# 功能：创建三个智能体，实现反馈循环机制，直到获得批准才进入下一步

import asyncio

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.conditions import MaxMessageTermination
from autogen_agentchat.teams import DiGraphBuilder, GraphFlow
from autogen_ext.models.openai import OpenAIChatCompletionClient
from llm import get_llm

async def main():
    # 使用 OpenAI 模型客户端初始化智能体
    api_key = "1932375702800756788"
    model_client = get_llm("deepseek-v3-friday", api_key)
    agent_a = AssistantAgent(
        "A",
        model_client=model_client,
        system_message="你是一个有用的助手。",
    )
    agent_b = AssistantAgent(
        "B",
        model_client=model_client,
        system_message="对输入内容提供反馈，如果你的反馈已经得到处理，"
        "请说'批准'，否则请提供拒绝的理由。",
    )
    agent_c = AssistantAgent(
        "C", model_client=model_client, system_message="将最终产品翻译成韩文。"
    )

    # 创建带有条件退出的循环图：A -> B -> C（"批准"），B -> A（其他情况）
    builder = DiGraphBuilder()
    builder.add_node(agent_a).add_node(agent_b).add_node(agent_c)
    builder.add_edge(agent_a, agent_b)

    # 使用字符串创建条件边
    builder.add_edge(agent_b, agent_c, condition=lambda msg: "批准" in msg.to_model_text())
    builder.add_edge(agent_b, agent_a, condition=lambda msg: "批准" not in msg.to_model_text())

    builder.set_entry_point(agent_a)
    graph = builder.build()

    # 使用有向图创建 GraphFlow 团队
    team = GraphFlow(
        participants=[agent_a, agent_b, agent_c],
        graph=graph,
        termination_condition=MaxMessageTermination(20),  # 最多20条消息以避免无限循环
    )

    # 运行团队并打印事件
    async for event in team.run_stream(task="写一首关于AI智能体的短诗。"):
        print(event)
        print()


asyncio.run(main())
