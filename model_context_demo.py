"""
AutoGen Model Context 完整演示

这个演示程序展示了 autogen_core.model_context 的所有核心功能：
1. UnboundedChatCompletionContext - 无限制上下文
2. BufferedChatCompletionContext - 缓冲区限制上下文
3. HeadAndTailChatCompletionContext - 头尾保留上下文
4. TokenLimitedChatCompletionContext - Token限制上下文
5. 自定义上下文 - 推理模型上下文示例

Requirements:
pip install autogen-agentchat[openai] tiktoken
"""

import asyncio
from typing import List, Optional
from autogen_core.model_context import (
    UnboundedChatCompletionContext,
    BufferedChatCompletionContext, 
    HeadAndTailChatCompletionContext,
    TokenLimitedChatCompletionContext,
    ChatCompletionContext
)
from autogen_core.models import (
    SystemMessage,
    UserMessage, 
    AssistantMessage,
    LLMMessage
)
from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient


class CustomReasoningModelContext(UnboundedChatCompletionContext):
    """
    自定义推理模型上下文 - 过滤掉 AssistantMessage 中的 thought 字段
    适用于 DeepSeek R1 等推理模型，这些模型会生成很长的思考过程
    """
    
    async def get_messages(self) -> List[LLMMessage]:
        messages = await super().get_messages()
        # 过滤掉思考字段以减少后续推理的token使用
        filtered_messages: List[LLMMessage] = []
        for message in messages:
            if isinstance(message, AssistantMessage):
                # 创建新的消息对象，不包含 thought 字段
                filtered_message = AssistantMessage(
                    content=message.content,
                    source=message.source
                )
                filtered_messages.append(filtered_message)
            else:
                filtered_messages.append(message)
        return filtered_messages


class MemoryEfficientContext(UnboundedChatCompletionContext):
    """
    内存高效的上下文 - 定期清理旧消息，保留关键信息
    """
    
    def __init__(self, max_history: int = 50, keep_system_messages: bool = True):
        super().__init__()
        self.max_history = max_history
        self.keep_system_messages = keep_system_messages
    
    async def get_messages(self) -> List[LLMMessage]:
        all_messages = await super().get_messages()
        
        if len(all_messages) <= self.max_history:
            return all_messages
        
        # 保留系统消息和最近的消息
        system_messages = []
        other_messages = []
        
        for msg in all_messages:
            if isinstance(msg, SystemMessage) and self.keep_system_messages:
                system_messages.append(msg)
            else:
                other_messages.append(msg)
        
        # 保留最近的消息
        recent_messages = other_messages[-(self.max_history - len(system_messages)):]
        
        return system_messages + recent_messages


async def demo_unbounded_context():
    """演示无限制上下文 - 保存所有对话历史"""
    print("=== 无限制上下文演示 ===")
    
    # 创建无限制上下文
    context = UnboundedChatCompletionContext()
    
    # 添加一系列消息
    messages = [
        SystemMessage(content="你是一个有用的助手"),
        UserMessage(content="你好，我想了解Python", source="user"),
        AssistantMessage(content="Python是一门强大的编程语言", source="assistant"),
        UserMessage(content="能告诉我Python的特点吗？", source="user"),
        AssistantMessage(content="Python具有简洁、易读、功能丰富等特点", source="assistant"),
        UserMessage(content="谢谢你的解释", source="user"),
    ]
    
    for msg in messages:
        await context.add_message(msg)
    
    # 获取所有消息
    stored_messages = await context.get_messages()
    
    print(f"总共存储了 {len(stored_messages)} 条消息:")
    for i, msg in enumerate(stored_messages, 1):
        msg_type = msg.__class__.__name__
        content = msg.content if hasattr(msg, 'content') else str(msg)
        print(f"{i}. [{msg_type}]: {content}")
    
    print()


async def demo_buffered_context():
    """演示缓冲区限制上下文 - 只保留最近的N条消息"""
    print("=== 缓冲区限制上下文演示 ===")
    
    # 创建缓冲区大小为3的上下文
    context = BufferedChatCompletionContext(buffer_size=3)
    
    # 添加5条消息，但只会保留最后3条
    messages = [
        UserMessage(content="消息1 - 这条会被丢弃", source="user"),
        UserMessage(content="消息2 - 这条也会被丢弃", source="user"),
        UserMessage(content="消息3 - 这条会保留", source="user"), 
        UserMessage(content="消息4 - 这条会保留", source="user"),
        UserMessage(content="消息5 - 这条会保留", source="user"),
    ]
    
    for i, msg in enumerate(messages, 1):
        await context.add_message(msg)
        stored = await context.get_messages()
        print(f"添加消息{i}后，缓冲区中有 {len(stored)} 条消息")
    
    # 最终的消息
    final_messages = await context.get_messages()
    print(f"\n最终缓冲区中的消息:")
    for i, msg in enumerate(final_messages, 1):
        print(f"{i}. {msg.content}")
    
    print()


async def demo_head_and_tail_context():
    """演示头尾保留上下文 - 保留开头N条和结尾M条消息"""
    print("=== 头尾保留上下文演示 ===")
    
    # 创建头部保留2条，尾部保留2条的上下文
    context = HeadAndTailChatCompletionContext(head_size=2, tail_size=2)
    
    # 添加8条消息
    messages = [
        SystemMessage(content="系统消息 - 头部1"),
        UserMessage(content="用户消息 - 头部2", source="user"),
        UserMessage(content="这条消息会被丢弃 - 中间1", source="user"),
        UserMessage(content="这条消息会被丢弃 - 中间2", source="user"),
        UserMessage(content="这条消息会被丢弃 - 中间3", source="user"),
        UserMessage(content="这条消息会被丢弃 - 中间4", source="user"),
        UserMessage(content="用户消息 - 尾部1", source="user"),
        UserMessage(content="用户消息 - 尾部2", source="user"),
    ]
    
    for msg in messages:
        await context.add_message(msg)
    
    # 获取保留的消息
    retained_messages = await context.get_messages()
    
    print(f"从 {len(messages)} 条消息中保留了 {len(retained_messages)} 条:")
    for i, msg in enumerate(retained_messages, 1):
        content = msg.content if hasattr(msg, 'content') else str(msg)
        msg_type = msg.__class__.__name__
        print(f"{i}. [{msg_type}]: {content}")
    
    print()


async def demo_token_limited_context():
    """演示Token限制上下文 - 根据Token数量限制上下文大小"""
    print("=== Token限制上下文演示 ===")
    
    # 注意：这需要一个真实的模型客户端来计算tokens
    # 如果没有API key，这部分会跳过
    try:
        model_client = OpenAIChatCompletionClient(
            model="gpt-3.5-turbo",
            # api_key="your_api_key_here"  # 设置你的API key
        )
        
        # 创建Token限制为100的上下文
        context = TokenLimitedChatCompletionContext(
            model_client=model_client,
            token_limit=100
        )
        
        # 添加一些长消息
        long_messages = [
            SystemMessage(content="你是一个专业的编程助手，擅长Python、JavaScript、Java等多种编程语言。"),
            UserMessage(content="请详细解释Python中的装饰器概念，包括其工作原理、使用场景和最佳实践。", source="user"),
            AssistantMessage(content="装饰器是Python中的一个强大特性，它允许你在不修改函数本身的情况下，为函数添加额外的功能。", source="assistant"),
            UserMessage(content="能给我一些装饰器的实际应用例子吗？比如在web开发中的使用。", source="user"),
            AssistantMessage(content="当然可以！装饰器在web开发中有很多应用，比如身份验证、缓存、日志记录等。", source="assistant"),
        ]
        
        for msg in long_messages:
            await context.add_message(msg)
        
        # 获取在token限制内的消息
        limited_messages = await context.get_messages()
        
        print(f"在Token限制内保留了 {len(limited_messages)} 条消息:")
        for i, msg in enumerate(limited_messages, 1):
            content = msg.content if hasattr(msg, 'content') else str(msg)
            content_preview = content[:50] + "..." if len(content) > 50 else content
            msg_type = msg.__class__.__name__
            print(f"{i}. [{msg_type}]: {content_preview}")
    
    except Exception as e:
        print(f"Token限制演示跳过 (需要API key): {e}")
    
    print()


async def demo_custom_reasoning_context():
    """演示自定义推理模型上下文 - 过滤思考过程"""
    print("=== 自定义推理模型上下文演示 ===")
    
    # 创建自定义推理上下文
    context = CustomReasoningModelContext()
    
    # 添加包含思考过程的消息（模拟推理模型的输出）
    messages = [
        SystemMessage(content="你是一个推理助手"),
        UserMessage(content="1+1等于多少？", source="user"),
        AssistantMessage(
            content="答案是2", 
            source="assistant",
            thought="让我思考一下这个问题。1+1是基本的加法运算。1个物体加上另外1个物体，总共就是2个物体。所以答案是2。"
        ),
        UserMessage(content="2+3等于多少？", source="user"),
        AssistantMessage(
            content="答案是5",
            source="assistant", 
            thought="这是另一个加法问题。2+3意味着我们有2个单位，再加上3个单位。2+3=5。"
        ),
    ]
    
    for msg in messages:
        await context.add_message(msg)
    
    # 获取过滤后的消息（不包含thought字段）
    filtered_messages = await context.get_messages()
    
    print("过滤后的消息（去除了思考过程）:")
    for i, msg in enumerate(filtered_messages, 1):
        msg_type = msg.__class__.__name__
        if isinstance(msg, AssistantMessage):
            print(f"{i}. [{msg_type}]: {msg.content}")
            print(f"    思考过程已被过滤: {'是' if not hasattr(msg, 'thought') or msg.thought is None else '否'}")
        else:
            content = msg.content if hasattr(msg, 'content') else str(msg)
            print(f"{i}. [{msg_type}]: {content}")
    
    print()


async def demo_memory_efficient_context():
    """演示内存高效上下文 - 智能清理历史消息"""
    print("=== 内存高效上下文演示 ===")
    
    # 创建内存高效上下文，最多保留5条消息
    context = MemoryEfficientContext(max_history=5, keep_system_messages=True)
    
    # 添加大量消息
    messages = [
        SystemMessage(content="重要的系统指令"),
        UserMessage(content="历史消息1", source="user"),
        UserMessage(content="历史消息2", source="user"), 
        UserMessage(content="历史消息3", source="user"),
        UserMessage(content="历史消息4", source="user"),
        UserMessage(content="历史消息5", source="user"),
        UserMessage(content="最新消息1", source="user"),
        UserMessage(content="最新消息2", source="user"),
        UserMessage(content="最新消息3", source="user"),
    ]
    
    for msg in messages:
        await context.add_message(msg)
    
    # 获取内存优化后的消息
    optimized_messages = await context.get_messages()
    
    print(f"从 {len(messages)} 条消息优化为 {len(optimized_messages)} 条:")
    for i, msg in enumerate(optimized_messages, 1):
        content = msg.content if hasattr(msg, 'content') else str(msg)
        msg_type = msg.__class__.__name__
        print(f"{i}. [{msg_type}]: {content}")
    
    print()


async def demo_context_with_agent():
    """演示在实际Agent中使用不同的model_context"""
    print("=== 在Agent中使用不同上下文演示 ===")
    
    try:
        # 创建模型客户端（需要API key）
        model_client = OpenAIChatCompletionClient(
            model="gpt-3.5-turbo"
            # api_key="your_api_key_here"
        )
        
        # 测试不同上下文配置的Agent
        contexts = [
            ("无限制上下文", UnboundedChatCompletionContext()),
            ("缓冲区上下文(3条)", BufferedChatCompletionContext(buffer_size=3)),
            ("头尾上下文(1+1)", HeadAndTailChatCompletionContext(head_size=1, tail_size=1)),
        ]
        
        for context_name, context in contexts:
            print(f"--- {context_name} ---")
            
            # 创建使用特定上下文的Agent
            agent = AssistantAgent(
                name="test_agent",
                model_client=model_client,
                model_context=context,
                system_message="你是一个简洁的助手，回答要简短。"
            )
            
            # 模拟多轮对话
            conversations = [
                "你好",
                "我想学编程", 
                "Python好学吗？",
                "给我一个建议"
            ]
            
            for user_input in conversations:
                try:
                    result = await agent.run(task=user_input)
                    response = result.messages[-1].content if result.messages else "无响应"
                    print(f"用户: {user_input}")
                    print(f"助手: {response}")
                except Exception as e:
                    print(f"对话失败: {e}")
                    break
            
            # 重置Agent以清理上下文
            await agent.on_reset()
            print()
    
    except Exception as e:
        print(f"Agent演示跳过 (需要API key): {e}")
        print()


async def demo_context_state_management():
    """演示上下文状态管理 - 保存和恢复"""
    print("=== 上下文状态管理演示 ===")
    
    # 创建上下文并添加消息
    context = BufferedChatCompletionContext(buffer_size=5)
    
    messages = [
        SystemMessage(content="系统消息"),
        UserMessage(content="用户问题1", source="user"),
        AssistantMessage(content="助手回答1", source="assistant"),
        UserMessage(content="用户问题2", source="user"),
    ]
    
    for msg in messages:
        await context.add_message(msg)
    
    # 显示当前状态
    current_messages = await context.get_messages()
    print("当前上下文状态:")
    for i, msg in enumerate(current_messages, 1):
        content = msg.content if hasattr(msg, 'content') else str(msg)
        print(f"{i}. {content}")
    
    print(f"\n上下文包含 {len(current_messages)} 条消息")
    
    # 注意：实际的状态保存/恢复功能依赖于具体的实现
    # 这里展示概念性的状态管理
    print("上下文状态管理完成")
    print()


async def main():
    """运行所有演示"""
    print("AutoGen Model Context 完整功能演示")
    print("=" * 60)
    print()
    
    # 运行各种上下文演示
    await demo_unbounded_context()
    await demo_buffered_context()
    await demo_head_and_tail_context()
    await demo_token_limited_context()
    await demo_custom_reasoning_context()
    await demo_memory_efficient_context()
    await demo_context_with_agent()
    await demo_context_state_management()
    
    print("所有演示完成！")
    print("\n核心要点总结:")
    print("1. UnboundedChatCompletionContext: 保存所有消息历史")
    print("2. BufferedChatCompletionContext: 只保留最近的N条消息")
    print("3. HeadAndTailChatCompletionContext: 保留开头M条和结尾N条消息")
    print("4. TokenLimitedChatCompletionContext: 根据Token数量限制上下文")
    print("5. 自定义上下文: 可以实现特殊的消息处理逻辑")
    print("6. 在Agent中使用不同上下文可以优化内存使用和性能")


if __name__ == "__main__":
    # 运行演示
    asyncio.run(main())