# 并行分发示例：演示如何创建一个扇出式的多智能体工作流（A -> B, A -> C）
# 功能：创建三个智能体，一个主智能体同时向两个子智能体分发任务，实现并行处理

import asyncio

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.conditions import MaxMessageTermination
from autogen_agentchat.teams import DiGraphBuilder, GraphFlow
from autogen_ext.models.openai import OpenAIChatCompletionClient
from llm import get_llm

async def main():
    # 使用 OpenAI 模型客户端初始化智能体
    api_key = "1932375702800756788"
    model_client = get_llm("deepseek-v3-friday", api_key)
    agent_a = AssistantAgent("A", model_client=model_client, system_message="你是一个有用的助手。")
    agent_b = AssistantAgent("B", model_client=model_client, system_message="将输入内容翻译成中文。")
    agent_c = AssistantAgent("C", model_client=model_client, system_message="将输入内容翻译成日文。")

    # 创建扇出流程的有向图 A -> (B, C)
    builder = DiGraphBuilder()
    builder.add_node(agent_a).add_node(agent_b).add_node(agent_c)
    builder.add_edge(agent_a, agent_b).add_edge(agent_a, agent_c)
    graph = builder.build()

    # 使用有向图创建 GraphFlow 团队
    team = GraphFlow(
        participants=[agent_a, agent_b, agent_c],
        graph=graph,
        termination_condition=MaxMessageTermination(5),
    )

    # 运行团队并打印事件
    async for event in team.run_stream(task="写一个关于猫的短故事。"):
        print(event)
        print()


asyncio.run(main())
